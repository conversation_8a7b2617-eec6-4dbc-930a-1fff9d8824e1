# Hello World Vue3

一个基于 Vue 3 + Vite + TypeScript + Element Plus 的现代化前端项目模板。

## 技术栈

### 核心技术
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 
- **包管理器**: npm
- **语言**: TypeScript
- **Node版本**: 14.21.3

### UI 框架
- **主UI库**: Element Plus 
- **图标**: Element Plus Icons 
- **样式预处理器**: Sass

### 状态管理与路由
- **状态管理**: Pinia 2.1.6 
- **路由**: Vue Router 4.2.4 (History模式 + 动态路由加载)

### 工具库
- **HTTP请求**: Axios

## 项目特性

- ✅ Vue 3 Composition API
- ✅ TypeScript 严格模式
- ✅ Vite 快速构建
- ✅ Element Plus UI 组件库
- ✅ Pinia 状态管理
- ✅ Vue Router 4 路由管理
- ✅ Sass 样式预处理
- ✅ ESLint 代码检查
- ✅ 响应式设计
- ✅ 热重载开发

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 项目结构

```
hello-world-vue3/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── components/         # 公共组件
│   ├── views/             # 页面组件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia 状态管理
│   ├── styles/            # 样式文件
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目说明
```

## 开发规范

### Vue 3 规范
- 优先使用 Composition API
- 组件文件使用 PascalCase 命名
- 使用 TypeScript 接口定义 Props 类型
- 使用 Vue 3 生命周期钩子
- 使用 ref、reactive 等响应式API

### TypeScript 规范
- 启用严格模式
- 使用接口和类型别名定义复杂类型
- 支持实验性装饰器语法

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
