import request from '@/utils/request'

// 分页查询客户列表
export const getCustomerList = (data) => {
  return request({
    url: '/customer/list',
    data
  })
}

// 新增客户
export const addCustomer = (data) => {
  return request({
    url: '/customer/add',
    data
  })
}

// 获取客户详情
export const getCustomerDetail = (data) => {
  return request({
    url: '/customer/detail',
    data
  })
}

// 更新客户信息
export const updateCustomer = (data) => {
  return request({
    url: '/customer/update',
    data
  })
}

// 删除客户
export const deleteCustomer = (data) => {
  return request({
    url: '/customer/delete',
    data
  })
}

// 获取客户选项列表（用于下拉框）
export const getCustomerOptions = () => {
  return request({
    url: '/customer/options',
    data: {}
  })
}
