import request from '@/utils/request'

// 获取仪表板数据
export const getDashboardData = () => {
  return request({
    url: '/dashboard/data',
    data: {}
  })
}

// 获取高价值客户列表
export const getValuableCustomers = (data) => {
  return request({
    url: '/dashboard/valuable-customers',
    data
  })
}

// 获取指定日期新增客户详情
export const getDailyCustomers = (data) => {
  return request({
    url: '/dashboard/daily-customers',
    data
  })
}

// 导出高价值客户Excel
export const exportValuableCustomers = () => {
  return request({
    url: '/dashboard/export-valuable-customers',
    data: {},
    responseType: 'blob'
  })
}
