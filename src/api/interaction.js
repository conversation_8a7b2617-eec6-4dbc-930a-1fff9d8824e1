import request from '@/utils/request'

// 分页查询互动记录列表
export const getInteractionList = (data) => {
  return request({
    url: '/interaction/list',
    data
  })
}

// 新增互动记录
export const addInteraction = (data) => {
  return request({
    url: '/interaction/add',
    data
  })
}

// 获取互动记录详情
export const getInteractionDetail = (data) => {
  return request({
    url: '/interaction/detail',
    data
  })
}

// 更新互动记录
export const updateInteraction = (data) => {
  return request({
    url: '/interaction/update',
    data
  })
}

// 删除互动记录
export const deleteInteraction = (data) => {
  return request({
    url: '/interaction/delete',
    data
  })
}
