import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout.vue'),
    meta: { requiresAuth: true },
    redirect: '/customer',
    children: [
      {
        path: '/customer',
        name: 'Customer',
        component: () => import('@/views/customer/CustomerList.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/customer/add',
        name: 'CustomerAdd',
        component: () => import('@/views/customer/CustomerAdd.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/customer/edit/:id',
        name: 'CustomerEdit',
        component: () => import('@/views/customer/CustomerEdit.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/interaction',
        name: 'Interaction',
        component: () => import('@/views/interaction/InteractionList.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/interaction/add',
        name: 'InteractionAdd',
        component: () => import('@/views/interaction/InteractionAdd.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/interaction/edit/:id',
        name: 'InteractionEdit',
        component: () => import('@/views/interaction/InteractionEdit.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { requiresAuth: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn()) {
    // 需要登录但未登录，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && userStore.isLoggedIn()) {
    // 已登录用户访问登录页，跳转到客户管理页
    next('/customer')
  } else {
    next()
  }
})

export default router
