<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>智能分析</h2>
    </div>

    <!-- 预警提示条 -->
    <el-alert
      v-if="dashboardData.alerts && dashboardData.alerts.showAlert"
      class="alert-banner"
      type="warning"
      :closable="true"
      show-icon
    >
      <template #title>
        <div class="alert-content">
          <span v-if="dashboardData.alerts.lostCustomerRate > 30">
            ⚠️ 已流失客户占比过高：{{ dashboardData.alerts.lostCustomerRate }}%
          </span>
          <span v-if="dashboardData.alerts.noNewCustomerDays > 7" class="alert-item">
            ⚠️ 已连续 {{ dashboardData.alerts.noNewCustomerDays }} 天无新增客户
          </span>
        </div>
      </template>
    </el-alert>

    <el-row :gutter="20">
      <!-- 图表区域 -->
      <el-col :span="16">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>近30日新增客户趋势</span>
            </div>
          </template>
          <div ref="chartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 高价值客户列表 -->
      <el-col :span="8">
        <el-card class="valuable-customers-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>高价值客户</span>
              <el-button type="primary" size="small" @click="handleExport">
                <el-icon><Download /></el-icon>
                导出Excel
              </el-button>
            </div>
          </template>
          
          <el-table
            v-loading="valuableCustomersLoading"
            :data="valuableCustomers"
            size="small"
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="name" label="客户姓名" width="80" />
            <el-table-column prop="companyName" label="公司" width="100" show-overflow-tooltip />
            <el-table-column prop="interactionCount" label="互动次数" width="80" align="center" />
            <el-table-column prop="lastInteractionTime" label="上次联系" width="100" show-overflow-tooltip />
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="valuableCustomersPagination.pageNum"
              v-model:page-size="valuableCustomersPagination.pageSize"
              :total="valuableCustomersPagination.total"
              :page-sizes="[10, 20, 30]"
              layout="prev, pager, next"
              small
              @size-change="handleValuableCustomersSizeChange"
              @current-change="handleValuableCustomersCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日期客户详情弹窗 -->
    <el-dialog
      v-model="dailyCustomersDialogVisible"
      :title="`${selectedDate} 新增客户详情`"
      width="600px"
    >
      <el-table
        v-loading="dailyCustomersLoading"
        :data="dailyCustomers"
        size="small"
        style="width: 100%"
      >
        <el-table-column prop="name" label="客户姓名" width="100" />
        <el-table-column prop="companyName" label="公司名称" width="150" />
        <el-table-column prop="phone" label="电话" width="120" />
        <el-table-column prop="source" label="来源" width="100" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getDashboardData, getValuableCustomers, getDailyCustomers, exportValuableCustomers } from '@/api/dashboard'

// 图表引用
const chartRef = ref()
let chartInstance = null

// 仪表板数据
const dashboardData = ref({})

// 高价值客户数据
const valuableCustomers = ref([])
const valuableCustomersLoading = ref(false)
const valuableCustomersPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 日期客户详情弹窗
const dailyCustomersDialogVisible = ref(false)
const dailyCustomers = ref([])
const dailyCustomersLoading = ref(false)
const selectedDate = ref('')

// 获取仪表板数据
const getDashboard = async () => {
  try {
    const response = await getDashboardData()
    if (response.success) {
      dashboardData.value = response.data
      await nextTick()
      initChart()
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
  }
}

// 获取高价值客户列表
const getValuableCustomersList = async () => {
  try {
    valuableCustomersLoading.value = true
    
    const params = {
      pageNum: valuableCustomersPagination.pageNum,
      pageSize: valuableCustomersPagination.pageSize
    }
    
    const response = await getValuableCustomers(params)
    
    if (response.success) {
      valuableCustomers.value = response.data.rows
      valuableCustomersPagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取高价值客户列表失败:', error)
  } finally {
    valuableCustomersLoading.value = false
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !dashboardData.value.customerTrend) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: '新增客户趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `${data.name}<br/>新增客户：${data.value}人`
      }
    },
    xAxis: {
      type: 'category',
      data: dashboardData.value.customerTrend.dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '客户数量'
    },
    series: [
      {
        name: '新增客户',
        type: 'line',
        data: dashboardData.value.customerTrend.counts,
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
  
  chartInstance.setOption(option)
  
  // 点击事件
  chartInstance.on('click', (params) => {
    selectedDate.value = params.name
    getDailyCustomersDetail(params.name)
  })
  
  // 响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

// 获取指定日期客户详情
const getDailyCustomersDetail = async (date) => {
  try {
    dailyCustomersLoading.value = true
    dailyCustomersDialogVisible.value = true
    
    const response = await getDailyCustomers({ date })
    
    if (response.success) {
      dailyCustomers.value = response.data
    }
  } catch (error) {
    console.error('获取日期客户详情失败:', error)
  } finally {
    dailyCustomersLoading.value = false
  }
}

// 导出高价值客户Excel
const handleExport = async () => {
  try {
    const response = await exportValuableCustomers()
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `高价值客户列表_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 高价值客户分页
const handleValuableCustomersSizeChange = (size) => {
  valuableCustomersPagination.pageSize = size
  valuableCustomersPagination.pageNum = 1
  getValuableCustomersList()
}

const handleValuableCustomersCurrentChange = (page) => {
  valuableCustomersPagination.pageNum = page
  getValuableCustomersList()
}

// 组件挂载时获取数据
onMounted(() => {
  getDashboard()
  getValuableCustomersList()
})

// 组件卸载时销毁图表
onUnmounted(() => {
  chartInstance?.dispose()
  window.removeEventListener('resize', () => {
    chartInstance?.resize()
  })
})
</script>

<style scoped>
.dashboard {
  height: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.alert-banner {
  margin-bottom: 20px;
}

.alert-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.alert-item {
  display: block;
}

.chart-card {
  height: 500px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.valuable-customers-card {
  height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 15px;
  text-align: center;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}
</style>
