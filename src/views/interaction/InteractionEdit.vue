<template>
  <div class="interaction-edit">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>编辑互动记录</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
    </div>

    <!-- 表单区域 -->
    <el-card v-loading="pageLoading" class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="interaction-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联客户" prop="customerId">
              <el-select
                v-model="form.customerId"
                placeholder="请选择客户"
                filterable
                style="width: 100%"
                :loading="customerOptionsLoading"
              >
                <el-option
                  v-for="item in customerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="互动类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择互动类型" style="width: 100%">
                <el-option label="电话" :value="0" />
                <el-option label="邮件" :value="1" />
                <el-option label="会议" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="内容摘要" prop="summary">
              <el-input
                v-model="form.summary"
                type="textarea"
                :rows="6"
                placeholder="请输入互动内容摘要"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="下次跟进时间" prop="nextFollowUpTime">
              <el-date-picker
                v-model="form.nextFollowUpTime"
                type="datetime"
                placeholder="请选择下次跟进时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ loading ? '更新中...' : '更新' }}
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getInteractionDetail, updateInteraction } from '@/api/interaction'
import { getCustomerOptions } from '@/api/customer'

const router = useRouter()
const route = useRoute()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)
const pageLoading = ref(false)
const customerOptionsLoading = ref(false)

// 互动记录ID
const interactionId = route.params.id

// 客户选项
const customerOptions = ref([])

// 表单数据
const form = reactive({
  id: '',
  customerId: '',
  type: '',
  summary: '',
  nextFollowUpTime: ''
})

// 表单验证规则
const rules = {
  customerId: [
    { required: true, message: '请选择关联客户', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择互动类型', trigger: 'change' }
  ],
  summary: [
    { required: true, message: '请输入内容摘要', trigger: 'blur' }
  ]
}

// 获取客户选项
const getCustomerOptionsList = async () => {
  try {
    customerOptionsLoading.value = true
    const response = await getCustomerOptions()
    if (response.success) {
      customerOptions.value = response.data
    }
  } catch (error) {
    console.error('获取客户选项失败:', error)
  } finally {
    customerOptionsLoading.value = false
  }
}

// 获取互动记录详情
const getDetail = async () => {
  try {
    pageLoading.value = true
    
    const response = await getInteractionDetail({ id: interactionId })
    
    if (response.success) {
      Object.assign(form, response.data)
    }
  } catch (error) {
    console.error('获取互动记录详情失败:', error)
    ElMessage.error('获取互动记录详情失败')
    router.push('/interaction')
  } finally {
    pageLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 调用更新接口
    const response = await updateInteraction(form)
    
    if (response.success) {
      ElMessage.success('更新互动记录成功')
      router.push('/interaction')
    }
  } catch (error) {
    console.error('更新互动记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/interaction')
}

// 组件挂载时获取数据
onMounted(async () => {
  if (interactionId) {
    await getCustomerOptionsList()
    await getDetail()
  } else {
    ElMessage.error('互动记录ID不存在')
    router.push('/interaction')
  }
})
</script>

<style scoped>
.interaction-edit {
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.form-card {
  max-width: 800px;
}

.interaction-form {
  padding: 20px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
