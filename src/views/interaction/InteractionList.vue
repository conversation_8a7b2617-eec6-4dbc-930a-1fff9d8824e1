<template>
  <div class="interaction-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>互动记录管理</h2>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="互动类型">
          <el-select v-model="searchForm.type" placeholder="请选择类型" clearable style="width: 150px">
            <el-option label="全部" :value="null" />
            <el-option label="电话" :value="0" />
            <el-option label="邮件" :value="1" />
            <el-option label="会议" :value="2" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关联客户">
          <el-select
            v-model="searchForm.customerId"
            placeholder="请选择客户"
            clearable
            filterable
            style="width: 200px"
            :loading="customerOptionsLoading"
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关键词搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入内容摘要关键词"
            clearable
            style="width: 250px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增互动记录
      </el-button>
    </div>

    <!-- 列表区域 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="type" label="互动类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="summary" label="内容摘要" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="customerName" label="关联客户" width="120">
          <template #default="{ row }">
            <el-link type="primary">{{ row.customerName }}</el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="nextFollowUpTime" label="下次跟进时间" width="160" />
        
        <el-table-column prop="createdTime" label="创建时间" width="160" />
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getInteractionList, deleteInteraction } from '@/api/interaction'
import { getCustomerOptions } from '@/api/customer'

const router = useRouter()

// 加载状态
const loading = ref(false)
const customerOptionsLoading = ref(false)

// 客户选项
const customerOptions = ref([])

// 搜索表单
const searchForm = reactive({
  type: null,
  customerId: null,
  keyword: ''
})

// 表格数据
const tableData = ref([])

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 获取互动类型标签类型
const getTypeTagType = (type) => {
  const types = {
    0: 'success',
    1: 'warning',
    2: 'info'
  }
  return types[type] || ''
}

// 获取互动类型文本
const getTypeText = (type) => {
  const texts = {
    0: '电话',
    1: '邮件',
    2: '会议'
  }
  return texts[type] || '未知'
}

// 获取客户选项
const getCustomerOptionsList = async () => {
  try {
    customerOptionsLoading.value = true
    const response = await getCustomerOptions()
    if (response.success) {
      customerOptions.value = response.data
    }
  } catch (error) {
    console.error('获取客户选项失败:', error)
  } finally {
    customerOptionsLoading.value = false
  }
}

// 获取互动记录列表
const getList = async () => {
  try {
    loading.value = true
    
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getInteractionList(params)
    
    if (response.success) {
      tableData.value = response.data.rows
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取互动记录列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  getList()
}

// 重置
const handleReset = () => {
  searchForm.type = null
  searchForm.customerId = null
  searchForm.keyword = ''
  pagination.pageNum = 1
  getList()
}

// 新增互动记录
const handleAdd = () => {
  router.push('/interaction/add')
}

// 编辑互动记录
const handleEdit = (row) => {
  router.push(`/interaction/edit/${row.id}`)
}

// 删除互动记录
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条互动记录吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteInteraction({ id: row.id })
    
    if (response.success) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除互动记录失败:', error)
    }
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getList()
}

// 组件挂载时获取数据
onMounted(() => {
  getCustomerOptionsList()
  getList()
})
</script>

<style scoped>
.interaction-list {
  height: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.action-bar {
  margin-bottom: 20px;
}

.table-card {
  flex: 1;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
