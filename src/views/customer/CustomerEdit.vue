<template>
  <div class="customer-edit">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>编辑客户</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
    </div>

    <!-- 表单区域 -->
    <el-card v-loading="pageLoading" class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="customer-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入客户姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="公司名称" prop="companyName">
              <el-input
                v-model="form.companyName"
                placeholder="请输入公司名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入电话号码"
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱地址"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择客户状态" style="width: 100%">
                <el-option label="潜在客户" :value="0" />
                <el-option label="已联系客户" :value="1" />
                <el-option label="已流失" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="来源" prop="source">
              <el-input
                v-model="form.source"
                placeholder="请输入客户来源"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ loading ? '更新中...' : '更新' }}
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCustomerDetail, updateCustomer } from '@/api/customer'

const router = useRouter()
const route = useRoute()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)
const pageLoading = ref(false)

// 客户ID
const customerId = route.params.id

// 表单数据
const form = reactive({
  id: '',
  name: '',
  companyName: '',
  phone: '',
  email: '',
  status: 0,
  source: '',
  remark: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择客户状态', trigger: 'change' }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: 'blur'
    }
  ]
}

// 获取客户详情
const getDetail = async () => {
  try {
    pageLoading.value = true
    
    const response = await getCustomerDetail({ id: customerId })
    
    if (response.success) {
      Object.assign(form, response.data)
    }
  } catch (error) {
    console.error('获取客户详情失败:', error)
    ElMessage.error('获取客户详情失败')
    router.push('/customer')
  } finally {
    pageLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 调用更新接口
    const response = await updateCustomer(form)
    
    if (response.success) {
      ElMessage.success('更新客户成功')
      router.push('/customer')
    }
  } catch (error) {
    console.error('更新客户失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/customer')
}

// 组件挂载时获取详情
onMounted(() => {
  if (customerId) {
    getDetail()
  } else {
    ElMessage.error('客户ID不存在')
    router.push('/customer')
  }
})
</script>

<style scoped>
.customer-edit {
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.form-card {
  max-width: 800px;
}

.customer-form {
  padding: 20px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
