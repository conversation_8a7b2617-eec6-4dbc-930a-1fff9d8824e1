import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  
  // 登录
  const login = async (loginData) => {
    try {
      const response = await request({
        url: '/user/login',
        data: loginData
      })
      
      if (response.success) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
        
        return response
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = {}
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }
  
  // 检查是否已登录
  const isLoggedIn = () => {
    return !!token.value
  }
  
  return {
    token,
    userInfo,
    login,
    logout,
    isLoggedIn
  }
})
