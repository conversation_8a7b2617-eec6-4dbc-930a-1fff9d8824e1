-- 用户表（用于系统登录和操作人员记录）
CREATE TABLE `user` (
                        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                        `username` VARCHAR(50) NOT NULL COMMENT '账号',
                        `password` VARCHAR(255) NOT NULL COMMENT '密码,md5存储',
                        `real_name` VARCHAR(50) NOT NULL COMMENT '姓名',
                        `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                        `created_by` BIGINT(20) NOT NULL COMMENT '创建人ID',
                        `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        `updated_by` BIGINT(20) NOT NULL COMMENT '更新人ID',
                        `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `uk_username` (`username`),
                        KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 客户表（核心业务表）
CREATE TABLE `customer` (
                            `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
                            `name` VARCHAR(100) NOT NULL COMMENT '客户姓名',
                            `company_name` VARCHAR(200) NOT NULL COMMENT '公司名称',
                            `phone` VARCHAR(20) DEFAULT NULL COMMENT '电话',
                            `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
                            `status` TINYINT(1) NOT NULL COMMENT '状态：0-潜在客户，1-已联系客户，2-已流失',
                            `source` VARCHAR(100) DEFAULT NULL COMMENT '来源',
                            `remark` TEXT COMMENT '备注',
                            `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                            `created_by` BIGINT(20) NOT NULL COMMENT '创建人ID',
                            `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` BIGINT(20) NOT NULL COMMENT '更新人ID',
                            `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`),
                            KEY `idx_created_by` (`created_by`),
                            KEY `idx_company_name` (`company_name`),
                            KEY `idx_name_company` (`name`, `company_name`),
                            KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

-- 互动记录表
CREATE TABLE `interaction` (
                               `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '互动ID',
                               `type` TINYINT(1) NOT NULL COMMENT '互动类型：0-电话，1-邮件，2-会议',
                               `summary` TEXT NOT NULL COMMENT '内容摘要',
                               `next_follow_up_time` DATETIME DEFAULT NULL COMMENT '下次跟进时间',
                               `customer_id` BIGINT(20) NOT NULL COMMENT '关联客户ID',
                               `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                               `created_by` BIGINT(20) NOT NULL COMMENT '创建人ID',
                               `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updated_by` BIGINT(20) NOT NULL COMMENT '更新人ID',
                               `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`),
                               KEY `idx_customer_id` (`customer_id`),
                               KEY `idx_created_by` (`created_by`),
                               KEY `idx_next_follow_up_time` (`next_follow_up_time`),
                               KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动记录表';

-- 高价值客户视图（用于智能分析模块）
CREATE VIEW `valuable_customer_view` AS
SELECT
    c.id,
    c.name,
    c.company_name,
    c.source,
    COUNT(i.id) AS interaction_count,
    MAX(i.created_time) AS last_interaction_time
FROM customer c
         JOIN interaction i ON c.id = i.customer_id
WHERE c.is_deleted = 0
  AND i.is_deleted = 0
  AND c.status = 1  -- 已联系客户
  AND i.created_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY c.id, c.name, c.company_name, c.source
HAVING COUNT(i.id) > 2
    OR c.source LIKE '%朋友推荐%';