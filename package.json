{"name": "customer-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "pinia": "^2.1.6", "echarts": "^5.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5"}}