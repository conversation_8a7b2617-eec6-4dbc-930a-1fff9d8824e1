## 技术栈

### 核心技术
- **框架**: Vue 3
- **构建工具**: Vite 
- **包管理器**: npm
- **node版本**: 14.21.3

### UI 框架
- **主UI库**: Element Plus 
- **图标**: Element Plus Icons 


### 后端请求规范
- **请求方式**: 所有请求全部用post
- **请求地址**: http://127.0.0.1:8080/
- **后端响应数据结构**:
  {
  "success": true,          // success代表请求处理状态，true代表请求处理成功，false代表请求处理失败
  "data": {},              // data代表响应的业务数据，仅当success为true时，data有值
  "errorCode": "000001"   // success为false时，errorCode中的内容是错误码
  "message": "请求成功"   //success为true时，message是固定的"请求成功"
  }

